/**
 * Hook para gerenciar notificações do usuário
 * Inclui real-time updates via Supabase subscriptions
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/services/supabase/client';
import { NotificationServiceClient } from '@/services/notifications/client/notification-service-client';
import type {
  Notification,
  NotificationFilters,
  PaginatedNotifications,
  NotificationServiceResponse
} from '@/services/notifications/types/notification-types';

interface UseNotificationsOptions {
  userId: string;
  filters?: NotificationFilters;
  realTime?: boolean;
}

interface UseNotificationsReturn {
  notifications: Notification[];
  loading: boolean;
  error: string | null;
  total: number;
  hasMore: boolean;
  page: number;
  refresh: () => Promise<void>;
  loadMore: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  archive: (notificationId: string) => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
}

export function useNotifications({
  userId,
  filters,
  realTime = true
}: UseNotificationsOptions): UseNotificationsReturn {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [page, setPage] = useState(1);

  // Função para carregar notificações
  const loadNotifications = useCallback(async (pageNum: number = 1, append: boolean = false) => {
    try {
      setLoading(true);
      setError(null);

      const notificationService = new NotificationServiceClient();
      const response: NotificationServiceResponse<PaginatedNotifications> = await notificationService.getByUser(
        userId,
        { ...filters, page: pageNum }
      );

      if (response.success && response.data) {
        const newNotifications = response.data.data;
        
        if (append) {
          setNotifications(prev => [...prev, ...newNotifications]);
        } else {
          setNotifications(newNotifications);
        }
        
        setTotal(response.data.total);
        setHasMore(response.data.hasMore);
        setPage(pageNum);
      } else {
        setError(response.error || 'Erro ao carregar notificações');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  }, [userId, filters]);

  // Função para atualizar uma notificação específica
  const updateNotificationInList = useCallback((notificationId: string, updates: Partial<Notification>) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, ...updates }
          : notification
      )
    );
  }, []);

  // Função para remover uma notificação da lista
  const removeNotificationFromList = useCallback((notificationId: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== notificationId));
    setTotal(prev => prev - 1);
  }, []);

  // Marcar como lida
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const notificationService = new NotificationServiceClient();
      const response = await notificationService.markAsRead(notificationId, userId);

      if (response.success) {
        updateNotificationInList(notificationId, {
          status: 'read',
          read_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      } else {
        setError(response.error || 'Erro ao marcar como lida');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    }
  }, [userId, updateNotificationInList]);

  // Marcar todas como lidas
  const markAllAsRead = useCallback(async () => {
    try {
      const notificationService = new NotificationServiceClient();
      const response = await notificationService.markAllAsRead(userId);

      if (response.success) {
        const now = new Date().toISOString();
        setNotifications(prev =>
          prev.map(notification =>
            notification.status === 'unread'
              ? { ...notification, status: 'read', read_at: now, updated_at: now }
              : notification
          )
        );
      } else {
        setError(response.error || 'Erro ao marcar todas como lidas');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    }
  }, [userId]);

  // Arquivar notificação
  const archive = useCallback(async (notificationId: string) => {
    try {
      const notificationService = new NotificationServiceClient();
      const response = await notificationService.archive(notificationId, userId);

      if (response.success) {
        updateNotificationInList(notificationId, {
          status: 'archived',
          updated_at: new Date().toISOString()
        });
      } else {
        setError(response.error || 'Erro ao arquivar notificação');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    }
  }, [userId, updateNotificationInList]);

  // Deletar notificação
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      const notificationService = new NotificationServiceClient();
      const response = await notificationService.delete(notificationId, userId);

      if (response.success) {
        removeNotificationFromList(notificationId);
      } else {
        setError(response.error || 'Erro ao deletar notificação');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    }
  }, [userId, removeNotificationFromList]);

  // Refresh
  const refresh = useCallback(async () => {
    await loadNotifications(1, false);
  }, [loadNotifications]);

  // Load more
  const loadMore = useCallback(async () => {
    if (hasMore && !loading) {
      await loadNotifications(page + 1, true);
    }
  }, [hasMore, loading, page, loadNotifications]);

  // Carregar notificações iniciais
  useEffect(() => {
    if (userId) {
      loadNotifications();
    }
  }, [userId, loadNotifications]);

  // Setup real-time subscription
  useEffect(() => {
    if (!realTime || !userId) return;

    const supabase = createClient();
    
    const subscription = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            const newNotification = payload.new as Notification;
            setNotifications(prev => [newNotification, ...prev]);
            setTotal(prev => prev + 1);
          } else if (payload.eventType === 'UPDATE') {
            const updatedNotification = payload.new as Notification;
            updateNotificationInList(updatedNotification.id, updatedNotification);
          } else if (payload.eventType === 'DELETE') {
            const deletedNotification = payload.old as Notification;
            removeNotificationFromList(deletedNotification.id);
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [realTime, userId, updateNotificationInList, removeNotificationFromList]);

  return {
    notifications,
    loading,
    error,
    total,
    hasMore,
    page,
    refresh,
    loadMore,
    markAsRead,
    markAllAsRead,
    archive,
    deleteNotification
  };
}
