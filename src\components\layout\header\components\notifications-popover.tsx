'use client';

import { Fragment } from "react";
import { Popover, PopoverButton, PopoverPanel, Transition } from "@headlessui/react";
import { BellIcon } from "@heroicons/react/24/outline";
import { CheckCircleIcon, XCircleIcon, InformationCircleIcon, ExclamationTriangleIcon } from "@heroicons/react/20/solid";
import Link from "next/link";
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { useUserMetadata } from "@/hooks/user/Auth/useUserMetadata";
import { useNotificationCount } from "@/hooks/notifications/use-notification-count";
import { useNotifications } from "@/hooks/notifications/use-notifications";
import { formatDistanceToNow } from "date-fns";
import type { NotificationCategory } from "@/services/notifications/types/notification-types";

// Função para obter ícone baseado na categoria
function getCategoryIcon(category: NotificationCategory) {
  switch (category) {
    case 'success':
      return <CheckCircleIcon className="h-5 w-5 sm:h-6 sm:w-6 text-green-500" />;
    case 'error':
      return <XCircleIcon className="h-5 w-5 sm:h-6 sm:w-6 text-red-500" />;
    case 'alert':
      return <ExclamationTriangleIcon className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-500" />;
    case 'info':
    case 'reminder':
    default:
      return <InformationCircleIcon className="h-5 w-5 sm:h-6 sm:w-6 text-blue-500" />;
  }
}

// Função para formatar tempo relativo
function formatRelativeTime(dateString: string) {
  try {
    return formatDistanceToNow(new Date(dateString), {
      addSuffix: true
    });
  } catch {
    return 'Agora mesmo';
  }
}

export function NotificationsPopover() {
  const { primaryColor } = useTenantTheme();
  const { metadata } = useUserMetadata();

  // Hooks de notificações - só executam se temos um usuário
  const { count, loading: countLoading } = useNotificationCount({
    userId: metadata?.id || '',
    realTime: true
  });

  const {
    notifications,
    loading: notificationsLoading,
    markAsRead,
    markAllAsRead
  } = useNotifications({
    userId: metadata?.id || '',
    filters: { limit: 5, status: ['unread'] }, // Apenas 5 mais recentes não lidas
    realTime: true
  });

  // Se não temos usuário, não renderizar
  if (!metadata?.id) {
    return null;
  }

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Erro ao marcar todas como lidas:', error);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead(notificationId);
    } catch (error) {
      console.error('Erro ao marcar como lida:', error);
    }
  };

  return (
    <Popover className="dropdown-overlay relative outline-none">
      <PopoverButton className="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500 focus:outline-none dark:text-gray-500 dark:hover:text-gray-400 outline-none">
        <span className="sr-only">Ver notificações</span>
        <div className="relative">
          <BellIcon className="h-6 w-6" aria-hidden="true" />
          {!countLoading && count > 0 && (
            <span
              className="absolute -top-1.5 -right-1.5 flex h-4 w-4 items-center justify-center rounded-full text-[10px] font-medium text-white"
              style={{ backgroundColor: primaryColor || '#ef4444' }}
            >
              {count > 99 ? '99+' : count}
            </span>
          )}
        </div>
      </PopoverButton>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-200"
        enterFrom="opacity-0 translate-y-1"
        enterTo="opacity-100 translate-y-0"
        leave="transition ease-in duration-150"
        leaveFrom="opacity-100 translate-y-0"
        leaveTo="opacity-0 translate-y-1"
      >
        <PopoverPanel className="dropdown-overlay fixed sm:absolute right-0 sm:right-0 top-16 sm:top-auto sm:mt-2 w-screen sm:w-80 transform sm:max-w-sm">
          <div className="overflow-hidden rounded-none sm:rounded-lg shadow-xl ring-1 ring-border/20 bg-white/95 backdrop-blur-sm dark:bg-gray-800/95 mx-0 sm:mx-0">
            <div className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Notificações</h3>
                {notifications.length > 0 && (
                  <button
                    onClick={handleMarkAllAsRead}
                    className="text-xs hover:opacity-80 transition-opacity"
                    style={{ color: primaryColor || '#2563eb' }}
                  >
                    Marcar todas como lidas
                  </button>
                )}
              </div>

              <div className="mt-3 sm:mt-4 space-y-2 sm:space-y-3">
                {notificationsLoading ? (
                  // Loading skeleton
                  <div className="space-y-2">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex gap-2 sm:gap-3 rounded-md p-2">
                        <div className="flex-shrink-0">
                          <div className="h-5 w-5 sm:h-6 sm:w-6 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />
                        </div>
                        <div className="flex-1 min-w-0 space-y-1">
                          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4" />
                          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/2" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : notifications.length === 0 ? (
                  // Estado vazio
                  <div className="text-center py-6">
                    <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                      Nenhuma notificação
                    </h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Você está em dia com tudo!
                    </p>
                  </div>
                ) : (
                  // Lista de notificações
                  notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className="flex gap-2 sm:gap-3 rounded-md p-2 hover:bg-muted/60 dark:hover:bg-gray-700 transition-colors duration-150 cursor-pointer"
                      onClick={() => handleMarkAsRead(notification.id)}
                    >
                      <div className="flex-shrink-0">
                        {getCategoryIcon(notification.category)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {notification.title}
                        </p>
                        <p className="mt-0.5 text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                          {notification.message}
                        </p>
                        <p className="mt-0.5 text-xs text-gray-400 dark:text-gray-500">
                          {formatRelativeTime(notification.created_at)}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Footer - só mostra se há notificações ou se não está carregando */}
            {!notificationsLoading && (
              <div className="bg-muted/30 p-2 sm:p-3 dark:bg-gray-700">
                <Link
                  href="/notificacoes"
                  className="block w-full rounded-md p-2 text-center text-sm font-medium hover:opacity-90 transition-opacity"
                  style={{ color: primaryColor || '#2563eb' }}
                >
                  Ver todas as notificações
                </Link>
              </div>
            )}
          </div>
        </PopoverPanel>
      </Transition>
    </Popover>
  );
}